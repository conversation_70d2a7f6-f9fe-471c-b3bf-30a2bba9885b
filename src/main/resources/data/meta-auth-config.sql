-- <PERSON>a (Facebook) 授权配置
-- 插入Meta媒体认证信息到数据库

INSERT INTO media_auth_info (
    media_type,
    client_id,
    client_secret,
    redirect_uri,
    auth_url,
    token_url,
    scope,
    created_at,
    updated_at
) VALUES (
    'meta',
    'YOUR_META_APP_ID',
    'YOUR_META_APP_SECRET',
    'https://your-domain.com/api/media-auth/callback/meta',
    'https://www.facebook.com/v18.0/dialog/oauth',
    'https://graph.facebook.com/v18.0/oauth/access_token',
    'public_profile,email',
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    client_id = VALUES(client_id),
    client_secret = VALUES(client_secret),
    redirect_uri = VALUES(redirect_uri),
    auth_url = VALUES(auth_url),
    token_url = VALUES(token_url),
    scope = VALUES(scope),
    updated_at = NOW();

-- 也可以使用 'facebook' 作为媒体类型
INSERT INTO media_auth_info (
    media_type,
    client_id,
    client_secret,
    redirect_uri,
    auth_url,
    token_url,
    scope,
    created_at,
    updated_at
) VALUES (
    'facebook',
    'YOUR_META_APP_ID',
    'YOUR_META_APP_SECRET',
    'https://your-domain.com/api/media-auth/callback/meta',
    'https://www.facebook.com/v18.0/dialog/oauth',
    'https://graph.facebook.com/v18.0/oauth/access_token',
    'public_profile,email',
    NOW(),
    NOW()
) ON DUPLICATE KEY UPDATE
    client_id = VALUES(client_id),
    client_secret = VALUES(client_secret),
    redirect_uri = VALUES(redirect_uri),
    auth_url = VALUES(auth_url),
    token_url = VALUES(token_url),
    scope = VALUES(scope),
    updated_at = NOW();
