server:
  port: 8080

spring:
  application:
    name: media-auth-service

  # Apollo配置
  config:
    import: optional:apollo://application

  datasource:
    url: ***********************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

# Apollo配置
apollo:
  meta: ${apollo.meta:http://localhost:8080}
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
    namespaces: application,logging

# 日志配置 - 这些配置可以通过Apollo动态调整
logging:
  config: classpath:log4j2-spring.xml
  level:
    com.roai.aa: INFO
    com.roai.aa.controller: INFO
    com.roai.aa.service: INFO
    org.springframework: INFO
    org.hibernate: INFO
    org.hibernate.SQL: DEBUG
    com.ctrip.framework.apollo: INFO