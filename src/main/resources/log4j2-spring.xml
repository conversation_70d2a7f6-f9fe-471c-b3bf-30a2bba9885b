<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <!-- 日志文件路径 -->
        <Property name="LOG_HOME">${sys:user.home}/logs/media-auth-service</Property>
        <!-- 日志格式 -->
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
        <!-- 控制台日志格式 -->
        <Property name="CONSOLE_PATTERN">%d{HH:mm:ss.SSS} [%t] %highlight{%-5level} %style{%logger{36}}{cyan} - %msg%n</Property>
    </Properties>

    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- 应用日志文件 -->
        <RollingFile name="AppFile" fileName="${LOG_HOME}/app.log"
                     filePattern="${LOG_HOME}/app.%d{yyyy-MM-dd}.%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- 错误日志文件 -->
        <RollingFile name="ErrorFile" fileName="${LOG_HOME}/error.log"
                     filePattern="${LOG_HOME}/error.%d{yyyy-MM-dd}.%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- API接口日志文件 -->
        <RollingFile name="ApiFile" fileName="${LOG_HOME}/api.log"
                     filePattern="${LOG_HOME}/api.%d{yyyy-MM-dd}.%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>

        <!-- 异步Appender -->
        <AsyncLogger name="com.roai.aa.controller" level="INFO" additivity="false">
            <AppenderRef ref="ApiFile"/>
            <AppenderRef ref="Console"/>
        </AsyncLogger>
    </Appenders>

    <Loggers>
        <!-- API接口专用Logger -->
        <Logger name="com.roai.aa.controller" level="INFO" additivity="false">
            <AppenderRef ref="ApiFile"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- 业务逻辑Logger -->
        <Logger name="com.roai.aa.service" level="INFO" additivity="false">
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- 数据访问Logger -->
        <Logger name="com.roai.aa.repository" level="DEBUG" additivity="false">
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="Console"/>
        </Logger>

        <!-- Spring框架日志 -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="AppFile"/>
        </Logger>

        <!-- Hibernate日志 -->
        <Logger name="org.hibernate" level="INFO" additivity="false">
            <AppenderRef ref="AppFile"/>
        </Logger>

        <!-- SQL日志 -->
        <Logger name="org.hibernate.SQL" level="DEBUG" additivity="false">
            <AppenderRef ref="AppFile"/>
        </Logger>

        <!-- Apollo配置中心日志 -->
        <Logger name="com.ctrip.framework.apollo" level="INFO" additivity="false">
            <AppenderRef ref="AppFile"/>
        </Logger>

        <!-- Root Logger -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AppFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Root>
    </Loggers>
</Configuration>
