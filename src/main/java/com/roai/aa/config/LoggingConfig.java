package com.roai.aa.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configurator;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * 日志配置类
 * 支持通过Apollo动态调整日志级别
 */
@Slf4j
@Configuration
public class LoggingConfig {
    
    @PostConstruct
    public void init() {
        log.info("日志配置初始化完成，支持Apollo动态配置");
        
        // TODO: 后续可以添加Apollo配置监听器来动态调整日志级别
        // 当前先使用静态配置，确保基础功能正常工作
    }
    
    /**
     * 动态设置日志级别
     */
    private void setLogLevel(String loggerName, String level) {
        try {
            LoggerContext context = (LoggerContext) LogManager.getContext(false);
            org.apache.logging.log4j.Level log4jLevel = org.apache.logging.log4j.Level.toLevel(level.toUpperCase());
            
            if ("ROOT".equalsIgnoreCase(loggerName)) {
                Configurator.setRootLevel(log4jLevel);
            } else {
                Configurator.setLevel(loggerName, log4jLevel);
            }
            
            context.updateLoggers();
            log.info("成功设置日志级别: {} = {}", loggerName, level);
        } catch (Exception e) {
            log.error("设置日志级别失败: {} = {}", loggerName, level, e);
        }
    }
}
