package com.roai.aa.repository;

import com.roai.aa.entity.UserAuthRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserAuthRecordRepository extends JpaRepository<UserAuthRecord, Long> {
    List<UserAuthRecord> findByUserId(String userId);
    List<UserAuthRecord> findByUserIdAndMediaType(String userId, String mediaType);
    Optional<UserAuthRecord> findByUserIdAndMediaTypeAndStatus(String userId, String mediaType, UserAuthRecord.AuthStatus status);
}