package com.roai.aa.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * API接口日志切面
 * 记录所有Controller方法的输入参数和输出结果
 */
@Slf4j
@Aspect
@Component
public class ApiLoggingAspect {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 定义切点：所有Controller包下的方法
     */
    @Pointcut("execution(* com.roai.aa.controller..*(..))")
    public void apiMethods() {}

    /**
     * 环绕通知：记录API调用的详细信息
     */
    @Around("apiMethods()")
    public Object logApiCall(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().toShortString();
        
        // 获取请求信息
        HttpServletRequest request = getCurrentRequest();
        String requestId = generateRequestId();
        
        try {
            // 记录请求开始
            logRequestStart(requestId, methodName, joinPoint.getArgs(), request);
            
            // 执行方法
            Object result = joinPoint.proceed();
            
            // 记录请求成功
            long duration = System.currentTimeMillis() - startTime;
            logRequestSuccess(requestId, methodName, result, duration);
            
            return result;
            
        } catch (Exception e) {
            // 记录请求异常
            long duration = System.currentTimeMillis() - startTime;
            logRequestError(requestId, methodName, e, duration);
            throw e;
        }
    }

    /**
     * 记录请求开始
     */
    private void logRequestStart(String requestId, String methodName, Object[] args, HttpServletRequest request) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("requestId", requestId);
            logData.put("method", methodName);
            logData.put("httpMethod", request != null ? request.getMethod() : "UNKNOWN");
            logData.put("uri", request != null ? request.getRequestURI() : "UNKNOWN");
            logData.put("remoteAddr", request != null ? getClientIpAddress(request) : "UNKNOWN");
            logData.put("userAgent", request != null ? request.getHeader("User-Agent") : "UNKNOWN");
            
            // 记录请求参数（过滤敏感信息）
            if (args != null && args.length > 0) {
                logData.put("parameters", filterSensitiveData(Arrays.toString(args)));
            }
            
            // 记录请求头（过滤敏感信息）
            if (request != null) {
                Map<String, String> headers = new HashMap<>();
                Enumeration<String> headerNames = request.getHeaderNames();
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    if (!isSensitiveHeader(headerName)) {
                        headers.put(headerName, request.getHeader(headerName));
                    }
                }
                logData.put("headers", headers);
            }
            
            log.info("API请求开始: {}", objectMapper.writeValueAsString(logData));
            
        } catch (Exception e) {
            log.warn("记录API请求开始日志失败: {}", e.getMessage());
        }
    }

    /**
     * 记录请求成功
     */
    private void logRequestSuccess(String requestId, String methodName, Object result, long duration) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("requestId", requestId);
            logData.put("method", methodName);
            logData.put("status", "SUCCESS");
            logData.put("duration", duration + "ms");
            
            // 记录响应结果（过滤敏感信息）
            if (result != null) {
                String resultStr = objectMapper.writeValueAsString(result);
                logData.put("response", filterSensitiveData(resultStr));
            }
            
            log.info("API请求成功: {}", objectMapper.writeValueAsString(logData));
            
        } catch (Exception e) {
            log.warn("记录API请求成功日志失败: {}", e.getMessage());
        }
    }

    /**
     * 记录请求异常
     */
    private void logRequestError(String requestId, String methodName, Exception exception, long duration) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("requestId", requestId);
            logData.put("method", methodName);
            logData.put("status", "ERROR");
            logData.put("duration", duration + "ms");
            logData.put("errorType", exception.getClass().getSimpleName());
            logData.put("errorMessage", exception.getMessage());
            
            log.error("API请求异常: {}", objectMapper.writeValueAsString(logData), exception);
            
        } catch (Exception e) {
            log.warn("记录API请求异常日志失败: {}", e.getMessage());
        }
    }

    /**
     * 获取当前请求
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 过滤敏感数据
     */
    private String filterSensitiveData(String data) {
        if (data == null) return null;
        
        // 过滤密码、token等敏感信息
        return data.replaceAll("(?i)(password|token|secret|key)\"?\\s*[:=]\\s*\"?[^,\\s}]+", "$1\":\"***\"");
    }

    /**
     * 判断是否为敏感请求头
     */
    private boolean isSensitiveHeader(String headerName) {
        if (headerName == null) return true;
        
        String lowerName = headerName.toLowerCase();
        return lowerName.contains("authorization") || 
               lowerName.contains("token") || 
               lowerName.contains("password") ||
               lowerName.contains("secret");
    }
}
