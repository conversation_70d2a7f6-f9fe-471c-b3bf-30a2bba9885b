package com.roai.aa.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.roai.aa.entity.MediaAuthInfo;
import com.roai.aa.entity.UserAuthRecord;
import com.roai.aa.repository.MediaAuthInfoRepository;
import com.roai.aa.repository.UserAuthRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
public class GoogleAdsAuthService {

    @Autowired
    private MediaAuthInfoRepository mediaAuthInfoRepository;

    @Autowired
    private UserAuthRecordRepository userAuthRecordRepository;

    @Autowired
    private RestTemplate restTemplate;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理Google OAuth2.0授权服务器响应
     * 根据Google OAuth2.0文档第4步：交换授权码获取访问令牌
     *
     * @param code 授权码
     * @param userId 用户ID
     * @param mediaType 媒体类型
     */
    public void handleGoogleOAuthCallback(String code, String userId, String mediaType) {
        MediaAuthInfo authInfo = getMediaAuthInfo(mediaType);
        
        // 构建Google特定的请求参数
        MultiValueMap<String, String> requestBody = buildGoogleTokenRequest(code, authInfo);
        
        // 发送请求并处理响应
        String response = sendTokenRequest(authInfo.getTokenUrl(), requestBody);
        
        // 解析并保存token信息
        saveTokenResponse(userId, mediaType, response);
    }

    private MediaAuthInfo getMediaAuthInfo(String mediaType) {
        return mediaAuthInfoRepository.findByMediaType(mediaType)
                .orElseThrow(() -> new RuntimeException("不支持的媒体类型: " + mediaType));
    }

    private MultiValueMap<String, String> buildGoogleTokenRequest(String code, MediaAuthInfo authInfo) {
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        requestBody.add("code", code);
        requestBody.add("client_id", authInfo.getClientId());
        requestBody.add("client_secret", authInfo.getClientSecret());
        requestBody.add("redirect_uri", authInfo.getRedirectUri());
        requestBody.add("grant_type", "authorization_code");
        return requestBody;
    }

    private String sendTokenRequest(String tokenUrl, MultiValueMap<String, String> requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            return restTemplate.postForObject(tokenUrl, request, String.class);
        } catch (Exception e) {
            log.error("Google token请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取访问令牌失败: " + e.getMessage());
        }
    }

    private void saveTokenResponse(String userId, String mediaType, String response) {
        try {
            // 解析Google响应格式
            JsonNode jsonNode = objectMapper.readTree(response);
            
            UserAuthRecord record = userAuthRecordRepository
                    .findByUserIdAndMediaType(userId, mediaType)
                    .stream()
                    .findFirst()
                    .orElse(new UserAuthRecord());

            record.setUserId(userId);
            record.setMediaType(mediaType);
            record.setAccessToken(jsonNode.get("access_token").asText());
            
            if (jsonNode.has("refresh_token")) {
                record.setRefreshToken(jsonNode.get("refresh_token").asText());
            }
            
            if (jsonNode.has("expires_in")) {
                int expiresIn = jsonNode.get("expires_in").asInt();
                record.setTokenExpiresAt(LocalDateTime.now().plusSeconds(expiresIn));
            }
            
            record.setStatus(UserAuthRecord.AuthStatus.AUTHORIZED);
            record.setUpdatedAt(LocalDateTime.now());

            userAuthRecordRepository.save(record);
            
        } catch (Exception e) {
            log.error("保存Google token失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存令牌信息失败: " + e.getMessage());
        }
    }
}