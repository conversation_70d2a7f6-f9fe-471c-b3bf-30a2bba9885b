package com.roai.aa.service;

import com.roai.aa.entity.MediaAuthInfo;
import com.roai.aa.entity.UserAuthRecord;
import com.roai.aa.repository.MediaAuthInfoRepository;
import com.roai.aa.repository.UserAuthRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
public class TikTokAuthService {

    @Autowired
    private MediaAuthInfoRepository mediaAuthInfoRepository;

    @Autowired
    private UserAuthRecordRepository userAuthRecordRepository;

    /**
     * 处理TikTok OAuth2.0授权服务器响应
     * 根据TikTok OAuth2.0文档：交换授权码获取访问令牌
     *
     * @param code 授权码
     * @param userId 用户ID
     * @param mediaType 媒体类型
     */
    public void handleTikTokOAuthCallback(String code, String userId, String mediaType) {
        // 获取媒体认证信息
        Optional<MediaAuthInfo> authInfoOpt = mediaAuthInfoRepository.findByMediaType(mediaType);
        if (!authInfoOpt.isPresent()) {
            throw new RuntimeException("不支持的媒体类型: " + mediaType);
        }

        MediaAuthInfo authInfo = authInfoOpt.get();

        // 构建请求参数，使用TikTok API要求的参数
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("app_id", authInfo.getClientId());
        requestBody.put("secret", authInfo.getClientSecret());
        requestBody.put("auth_code", code);

        // 发送POST请求到token端点
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);

        RestTemplate restTemplate = new RestTemplate();
        String tokenUrl = authInfo.getTokenUrl();
        String response = restTemplate.postForObject(tokenUrl, request, String.class);

        // 解析响应并保存令牌信息
        // 创建或更新用户授权记录
        UserAuthRecord record = userAuthRecordRepository
                .findByUserIdAndMediaType(userId, mediaType)
                .stream()
                .findFirst()
                .orElse(new UserAuthRecord());

        record.setUserId(userId);
        record.setMediaType(mediaType);
        // 这里需要解析响应中的access_token、refresh_token等信息
        // 为简化示例，我们将响应保存到accessToken字段中
        record.setAccessToken(response);
        record.setStatus(UserAuthRecord.AuthStatus.AUTHORIZED);

        userAuthRecordRepository.save(record);
    }
}