package com.roai.aa.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.roai.aa.entity.MediaAuthInfo;
import com.roai.aa.entity.UserAuthRecord;
import com.roai.aa.repository.MediaAuthInfoRepository;
import com.roai.aa.repository.UserAuthRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class TikTokAuthService {

    private final MediaAuthInfoRepository mediaAuthInfoRepository;
    private final UserAuthRecordRepository userAuthRecordRepository;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理TikTok OAuth2.0授权服务器响应
     * 根据TikTok OAuth2.0文档：交换授权码获取访问令牌
     */
    public void handleTikTokOAuthCallback(String code, String userId, String mediaType) {
        MediaAuthInfo authInfo = getMediaAuthInfo(mediaType);
        
        // 构建TikTok特定的请求参数
        Map<String, String> requestBody = buildTikTokTokenRequest(code, authInfo);
        
        // 发送请求并处理响应
        String response = sendTokenRequest(authInfo.getTokenUrl(), requestBody);
        
        // 解析并保存token信息
        saveTokenResponse(userId, mediaType, response);
    }

    private MediaAuthInfo getMediaAuthInfo(String mediaType) {
        return mediaAuthInfoRepository.findByMediaType(mediaType)
                .orElseThrow(() -> new RuntimeException("不支持的媒体类型: " + mediaType));
    }

    private Map<String, String> buildTikTokTokenRequest(String code, MediaAuthInfo authInfo) {
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("app_id", authInfo.getClientId());
        requestBody.put("secret", authInfo.getClientSecret());
        requestBody.put("auth_code", code);
        requestBody.put("grant_type", "authorization_code");
        return requestBody;
    }

    private String sendTokenRequest(String tokenUrl, Map<String, String> requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            String response = restTemplate.postForObject(tokenUrl, request, String.class);
            log.info("TikTok token请求成功");
            return response;
        } catch (Exception e) {
            log.error("TikTok token请求失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取访问令牌失败: " + e.getMessage());
        }
    }

    private void saveTokenResponse(String userId, String mediaType, String response) {
        try {
            // 解析TikTok响应格式
            JsonNode jsonNode = objectMapper.readTree(response);
            
            // 检查TikTok API响应状态
            if (jsonNode.has("code") && jsonNode.get("code").asInt() != 0) {
                String errorMessage = jsonNode.has("message") ? 
                    jsonNode.get("message").asText() : "TikTok API返回错误";
                throw new RuntimeException("TikTok授权失败: " + errorMessage);
            }
            
            JsonNode dataNode = jsonNode.get("data");
            if (dataNode == null) {
                throw new RuntimeException("TikTok响应格式错误：缺少data字段");
            }
            
            UserAuthRecord record = userAuthRecordRepository
                    .findByUserIdAndMediaType(userId, mediaType)
                    .stream()
                    .findFirst()
                    .orElse(new UserAuthRecord());

            record.setUserId(userId);
            record.setMediaType(mediaType);
            
            if (dataNode.has("access_token")) {
                record.setAccessToken(dataNode.get("access_token").asText());
            }
            
            if (dataNode.has("refresh_token")) {
                record.setRefreshToken(dataNode.get("refresh_token").asText());
            }
            
            if (dataNode.has("expires_in")) {
                int expiresIn = dataNode.get("expires_in").asInt();
                record.setTokenExpiresAt(LocalDateTime.now().plusSeconds(expiresIn));
            }
            
            record.setStatus(UserAuthRecord.AuthStatus.AUTHORIZED);
            record.setUpdatedAt(LocalDateTime.now());

            userAuthRecordRepository.save(record);
            log.info("TikTok token保存成功: userId={}, mediaType={}", userId, mediaType);
            
        } catch (Exception e) {
            log.error("保存TikTok token失败: userId={}, mediaType={}", userId, mediaType, e);
            throw new RuntimeException("保存令牌信息失败: " + e.getMessage());
        }
    }
}