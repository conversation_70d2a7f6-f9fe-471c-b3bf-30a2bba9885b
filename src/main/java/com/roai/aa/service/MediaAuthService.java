package com.roai.aa.service;

import com.roai.aa.dto.AuthInfoResponse;
import com.roai.aa.entity.MediaAuthInfo;
import com.roai.aa.entity.UserAuthRecord;
import com.roai.aa.repository.MediaAuthInfoRepository;
import com.roai.aa.repository.UserAuthRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Service
public class MediaAuthService {
    
    @Autowired
    private MediaAuthInfoRepository mediaAuthInfoRepository;
    
    @Autowired
    private UserAuthRecordRepository userAuthRecordRepository;
    
    @Autowired
    private GoogleAdsAuthService googleAdsAuthService;
    
    @Autowired
    private TikTokAuthService tikTokAuthService;
    
    public AuthInfoResponse getAuthInfo(String mediaType, String userId) {
        if (mediaType == null || mediaType.isEmpty()) {
            throw new IllegalArgumentException("媒体类型不能为空");
        }
        
        if (userId == null || userId.isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        
        MediaAuthInfo authInfo = mediaAuthInfoRepository.findByMediaType(mediaType)
            .orElseThrow(() -> new IllegalStateException("不支持的媒体类型: " + mediaType));
        
        // 生成state参数，包含用户ID和媒体类型信息
        String state = generateState(userId, mediaType);
        
        return new AuthInfoResponse(
            authInfo.getClientId(),
            authInfo.getRedirectUri(),
            authInfo.getAuthUrl(),
            authInfo.getScope(),
            state
        );
    }
    
    public List<UserAuthRecord> getUserAuthRecords(String userId) {
        return userAuthRecordRepository.findByUserId(userId);
    }
    
    public void handleAuthCallback(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            throw new IllegalArgumentException("回调参数不能为空");
        }
        
        String state = params.get("state");

        // 解析state获取用户ID和媒体类型
        String[] stateInfo = parseState(state);
        String userId = stateInfo[0];
        String mediaType = stateInfo[1];
        
        // 根据媒体类型使用不同的处理方式
        if ("google-ads".equals(mediaType)) {
            String code = params.get("code");
            String error = params.get("error");
            if (error != null) {
                throw new RuntimeException("授权失败: " + error);
            }

            if (code == null || state == null) {
                throw new RuntimeException("缺少必要的回调参数");
            }
            // 使用Google Ads专用服务处理OAuth2.0第4步
            googleAdsAuthService.handleGoogleOAuthCallback(code, userId, mediaType);
        } else if ("tiktok".equals(mediaType)) {
            String code = params.get("auth_code");
            if (code == null) {
                throw new RuntimeException("授权失败");
            }
            // 使用TikTok专用服务处理OAuth2.0授权
            tikTokAuthService.handleTikTokOAuthCallback(code, userId, mediaType);
        } else {
            // 其他媒体类型的默认处理方式
            // 创建或更新用户授权记录
            Optional<UserAuthRecord> recordOpt = userAuthRecordRepository
                .findByUserIdAndMediaType(userId, mediaType)
                .stream()
                .findFirst();
            
            UserAuthRecord record = recordOpt.orElse(new UserAuthRecord());
            record.setUserId(userId);
            record.setMediaType(mediaType);
            record.setStatus(UserAuthRecord.AuthStatus.PENDING);
            
            userAuthRecordRepository.save(record);
            
            // TODO: 使用code换取access_token
            // 这里需要调用对应媒体平台的token接口
//            System.out.println("处理授权回调: userId=" + userId + ", mediaType=" + mediaType + ", code=" + code);
        }
    }
    
    private String generateState(String userId, String mediaType) {
        if (userId == null || userId.isEmpty()) {
            throw new IllegalArgumentException("生成state失败：用户ID不能为空");
        }
        
        if (mediaType == null || mediaType.isEmpty()) {
            throw new IllegalArgumentException("生成state失败：媒体类型不能为空");
        }
        
        // 简单的state生成策略，实际项目中应该加密或使用更安全的方式
        return userId + ":" + mediaType + ":" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    private String[] parseState(String state) {
        if (state == null || state.isEmpty()) {
            throw new IllegalArgumentException("解析state失败：state参数不能为空");
        }
        
        String[] parts = state.split(":");
        if (parts.length < 2) {
            throw new IllegalArgumentException("无效的state参数: " + state);
        }
        
        if (parts[0] == null || parts[0].isEmpty()) {
            throw new IllegalArgumentException("无效的state参数，用户ID不能为空: " + state);
        }
        
        if (parts[1] == null || parts[1].isEmpty()) {
            throw new IllegalArgumentException("无效的state参数，媒体类型不能为空: " + state);
        }
        
        return new String[]{parts[0], parts[1]};
    }
}