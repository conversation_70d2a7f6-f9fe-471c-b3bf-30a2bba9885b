package com.roai.aa.service;

import com.roai.aa.dto.AuthInfoResponse;
import com.roai.aa.entity.MediaAuthInfo;
import com.roai.aa.entity.UserAuthRecord;
import com.roai.aa.repository.MediaAuthInfoRepository;
import com.roai.aa.repository.UserAuthRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class MediaAuthService {

    private final MediaAuthInfoRepository mediaAuthInfoRepository;
    private final UserAuthRecordRepository userAuthRecordRepository;
    private final GoogleAdsAuthService googleAdsAuthService;
    private final TikTokAuthService tikTokAuthService;
    private final RestTemplate restTemplate;

    // ==================== 公共接口方法 ====================
    
    /**
     * 获取媒体平台授权信息
     */
    public AuthInfoResponse getAuthInfo(String mediaType, String userId) {
        validateParameters(mediaType, userId);
        
        MediaAuthInfo authInfo = getMediaAuthInfoByType(mediaType);
        String state = generateState(userId, mediaType);
        
        return new AuthInfoResponse(
            authInfo.getClientId(),
            authInfo.getRedirectUri(),
            authInfo.getAuthUrl(),
            authInfo.getScope(),
            state
        );
    }
    
    /**
     * 获取用户授权记录
     */
    public List<UserAuthRecord> getUserAuthRecords(String userId) {
        if (userId == null || userId.isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userAuthRecordRepository.findByUserId(userId);
    }
    
    /**
     * 处理授权回调
     */
    public void handleAuthCallback(Map<String, String> params) {
        validateCallbackParams(params);
        
        String state = params.get("state");
        String[] stateInfo = parseState(state);
        String userId = stateInfo[0];
        String mediaType = stateInfo[1];
        
        // 根据媒体类型分发处理
        switch (mediaType.toLowerCase()) {
            case "google-ads":
                handleGoogleAdsCallback(params, userId, mediaType);
                break;
            case "tiktok":
                handleTikTokCallback(params, userId, mediaType);
                break;
            default:
                handleDefaultCallback(params, userId, mediaType);
                break;
        }
    }

    // ==================== Token管理方法 ====================
    
    /**
     * 刷新访问令牌
     */
    public boolean refreshAccessToken(String userId, String mediaType) {
        log.info("开始刷新访问令牌: userId={}, mediaType={}", userId, mediaType);

        try {
            UserAuthRecord record = getUserAuthRecord(userId, mediaType);
            if (record == null || record.getRefreshToken() == null || record.getRefreshToken().isEmpty()) {
                log.warn("refresh_token为空，无法刷新令牌: userId={}, mediaType={}", userId, mediaType);
                return false;
            }

            MediaAuthInfo authInfo = getMediaAuthInfoByType(mediaType);
            String newTokenResponse = callRefreshTokenApi(authInfo, record.getRefreshToken());

            if (newTokenResponse != null) {
                updateTokenInfo(record, newTokenResponse);
                log.info("访问令牌刷新成功: userId={}, mediaType={}", userId, mediaType);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("刷新访问令牌失败: userId={}, mediaType={}", userId, mediaType, e);
            return false;
        }
    }

    /**
     * 验证访问令牌是否有效
     */
    public boolean validateAccessToken(String userId, String mediaType) {
        log.info("验证访问令牌: userId={}, mediaType={}", userId, mediaType);

        try {
            UserAuthRecord record = getUserAuthRecord(userId, mediaType);
            if (record == null || record.getAccessToken() == null || record.getAccessToken().isEmpty()) {
                log.warn("access_token为空: userId={}, mediaType={}", userId, mediaType);
                return false;
            }

            // 检查令牌是否过期
            if (isTokenExpired(record)) {
                log.info("访问令牌已过期，尝试刷新: userId={}, mediaType={}", userId, mediaType);
                return refreshAccessToken(userId, mediaType);
            }

            // 调用API验证令牌
            return callValidateTokenApi(record.getAccessToken(), mediaType);

        } catch (Exception e) {
            log.error("验证访问令牌失败: userId={}, mediaType={}", userId, mediaType, e);
            return false;
        }
    }

    // ==================== 私有辅助方法 ====================
    
    private void validateParameters(String mediaType, String userId) {
        if (mediaType == null || mediaType.isEmpty()) {
            throw new IllegalArgumentException("媒体类型不能为空");
        }
        if (userId == null || userId.isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
    }
    
    private void validateCallbackParams(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            throw new IllegalArgumentException("回调参数不能为空");
        }
    }
    
    private MediaAuthInfo getMediaAuthInfoByType(String mediaType) {
        return mediaAuthInfoRepository.findByMediaType(mediaType)
            .orElseThrow(() -> new IllegalStateException("不支持的媒体类型: " + mediaType));
    }
    
    private UserAuthRecord getUserAuthRecord(String userId, String mediaType) {
        return userAuthRecordRepository
            .findByUserIdAndMediaType(userId, mediaType)
            .stream()
            .findFirst()
            .orElse(null);
    }
    
    private boolean isTokenExpired(UserAuthRecord record) {
        return record.getTokenExpiresAt() != null &&
               record.getTokenExpiresAt().isBefore(LocalDateTime.now());
    }
    
    // ==================== 回调处理方法 ====================
    
    private void handleGoogleAdsCallback(Map<String, String> params, String userId, String mediaType) {
        String code = params.get("code");
        String error = params.get("error");
        
        if (error != null) {
            throw new RuntimeException("Google Ads授权失败: " + error);
        }
        if (code == null) {
            throw new RuntimeException("缺少授权码参数");
        }
        
        googleAdsAuthService.handleGoogleOAuthCallback(code, userId, mediaType);
    }
    
    private void handleTikTokCallback(Map<String, String> params, String userId, String mediaType) {
        String code = params.get("auth_code");
        if (code == null) {
            throw new RuntimeException("TikTok授权失败：缺少auth_code参数");
        }
        
        tikTokAuthService.handleTikTokOAuthCallback(code, userId, mediaType);
    }
    
    private void handleDefaultCallback(Map<String, String> params, String userId, String mediaType) {
        String code = params.get("code");
        if (code == null) {
            throw new RuntimeException("授权失败：缺少授权码");
        }
        
        // 创建或更新用户授权记录
        UserAuthRecord record = userAuthRecordRepository
            .findByUserIdAndMediaType(userId, mediaType)
            .stream()
            .findFirst()
            .orElse(new UserAuthRecord());
        
        record.setUserId(userId);
        record.setMediaType(mediaType);
        record.setStatus(UserAuthRecord.AuthStatus.PENDING);
        
        userAuthRecordRepository.save(record);
        
        // TODO: 实现通用的token交换逻辑
        log.info("处理默认授权回调: userId={}, mediaType={}, code={}", userId, mediaType, code);
    }
    
    // ==================== State参数处理 ====================
    
    private String generateState(String userId, String mediaType) {
        if (userId == null || userId.isEmpty()) {
            throw new IllegalArgumentException("生成state失败：用户ID不能为空");
        }
        if (mediaType == null || mediaType.isEmpty()) {
            throw new IllegalArgumentException("生成state失败：媒体类型不能为空");
        }
        
        // 简单的state生成策略，实际项目中应该加密或使用更安全的方式
        return userId + ":" + mediaType + ":" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    private String[] parseState(String state) {
        if (state == null || state.isEmpty()) {
            throw new IllegalArgumentException("解析state失败：state参数不能为空");
        }
        
        String[] parts = state.split(":");
        if (parts.length < 2) {
            throw new IllegalArgumentException("无效的state参数: " + state);
        }
        
        if (parts[0] == null || parts[0].isEmpty()) {
            throw new IllegalArgumentException("无效的state参数，用户ID不能为空: " + state);
        }
        
        if (parts[1] == null || parts[1].isEmpty()) {
            throw new IllegalArgumentException("无效的state参数，媒体类型不能为空: " + state);
        }
        
        return new String[]{parts[0], parts[1]};
    }
    
    // ==================== Token API调用方法 ====================
    
    private void updateTokenInfo(UserAuthRecord record, String tokenResponse) {
        record.setAccessToken(tokenResponse);
        record.setStatus(UserAuthRecord.AuthStatus.AUTHORIZED);
        userAuthRecordRepository.save(record);
    }
    
    private String callRefreshTokenApi(MediaAuthInfo authInfo, String refreshToken) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/x-www-form-urlencoded");

            String requestBody = String.format(
                "grant_type=refresh_token&refresh_token=%s&client_id=%s&client_secret=%s",
                refreshToken, authInfo.getClientId(), authInfo.getClientSecret()
            );

            HttpEntity<String> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.exchange(
                authInfo.getTokenUrl(),
                HttpMethod.POST,
                request,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("刷新令牌API调用成功");
                return response.getBody();
            } else {
                log.warn("刷新令牌API调用失败: status={}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("调用刷新令牌API异常", e);
            return null;
        }
    }

    private boolean callValidateTokenApi(String accessToken, String mediaType) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + accessToken);

            HttpEntity<String> request = new HttpEntity<>(headers);

            String validateUrl = getValidateTokenUrl(mediaType);
            if (validateUrl == null) {
                log.warn("未配置令牌验证URL: mediaType={}", mediaType);
                return true; // 如果没有配置验证URL，默认认为有效
            }

            ResponseEntity<String> response = restTemplate.exchange(
                validateUrl,
                HttpMethod.GET,
                request,
                String.class
            );

            boolean isValid = response.getStatusCode().is2xxSuccessful();
            log.info("令牌验证结果: mediaType={}, isValid={}", mediaType, isValid);
            return isValid;

        } catch (Exception e) {
            log.error("验证令牌API调用异常: mediaType={}", mediaType, e);
            return false;
        }
    }

    private String getValidateTokenUrl(String mediaType) {
        return switch (mediaType.toLowerCase()) {
            case "google-ads" -> "https://www.googleapis.com/oauth2/v1/tokeninfo";
            case "tiktok" -> "https://business-api.tiktok.com/open_api/v1.3/oauth2/advertiser/get/";
            default -> null;
        };
    }
}