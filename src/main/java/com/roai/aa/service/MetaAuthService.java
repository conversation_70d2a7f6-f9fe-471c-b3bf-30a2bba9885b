package com.roai.aa.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.roai.aa.entity.MediaAuthInfo;
import com.roai.aa.entity.UserAuthRecord;
import com.roai.aa.repository.MediaAuthInfoRepository;
import com.roai.aa.repository.UserAuthRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * Meta (Facebook) 授权服务
 * 实现Facebook Login的手动流程
 * 参考文档: https://developers.facebook.com/docs/facebook-login/guides/advanced/manual-flow
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetaAuthService {

    private final MediaAuthInfoRepository mediaAuthInfoRepository;
    private final UserAuthRecordRepository userAuthRecordRepository;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 处理Meta OAuth2.0授权服务器响应
     * 根据Facebook Login文档：交换授权码获取访问令牌
     *
     * @param code 授权码
     * @param userId 用户ID
     * @param mediaType 媒体类型 (meta/facebook)
     */
    public void handleMetaOAuthCallback(String code, String userId, String mediaType) {
        log.info("开始处理Meta OAuth回调: userId={}, mediaType={}", userId, mediaType);
        
        try {
            // 获取媒体认证信息
            Optional<MediaAuthInfo> authInfoOpt = mediaAuthInfoRepository.findByMediaType(mediaType);
            if (authInfoOpt.isEmpty()) {
                throw new RuntimeException("不支持的媒体类型: " + mediaType);
            }

            MediaAuthInfo authInfo = authInfoOpt.get();

            // 第一步：使用authorization code交换access token
            String accessTokenResponse = exchangeCodeForAccessToken(authInfo, code);
            
            if (accessTokenResponse == null) {
                throw new RuntimeException("获取访问令牌失败");
            }

            // 解析访问令牌响应
            TokenResponse tokenResponse = parseTokenResponse(accessTokenResponse);
            
            // 第二步：使用access token获取用户信息进行验证
            String userInfo = getUserInfo(tokenResponse.getAccessToken());
            
            // 创建或更新用户授权记录
            saveUserAuthRecord(userId, mediaType, tokenResponse, userInfo);
            
            log.info("Meta OAuth授权处理成功: userId={}, mediaType={}", userId, mediaType);
            
        } catch (Exception e) {
            log.error("处理Meta OAuth回调失败: userId={}, mediaType={}", userId, mediaType, e);
            
            // 更新授权记录状态为失败
            updateAuthRecordStatus(userId, mediaType, UserAuthRecord.AuthStatus.EXPIRED);
            throw new RuntimeException("Meta授权处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用authorization code交换access token
     * Facebook Graph API endpoint: https://graph.facebook.com/v18.0/oauth/access_token
     */
    private String exchangeCodeForAccessToken(MediaAuthInfo authInfo, String code) {
        log.info("开始交换授权码获取访问令牌");
        
        try {
            // 构建请求参数
            MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
            requestBody.add("client_id", authInfo.getClientId());
            requestBody.add("client_secret", authInfo.getClientSecret());
            requestBody.add("redirect_uri", authInfo.getRedirectUri());
            requestBody.add("code", code);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/x-www-form-urlencoded");

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(requestBody, headers);

            // 发送POST请求到Facebook token端点
            String tokenUrl = authInfo.getTokenUrl();
            log.info("调用Facebook token端点: {}", tokenUrl);
            
            ResponseEntity<String> response = restTemplate.exchange(
                tokenUrl,
                HttpMethod.POST,
                request,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("成功获取访问令牌");
                return response.getBody();
            } else {
                log.error("获取访问令牌失败: status={}, body={}", 
                    response.getStatusCode(), response.getBody());
                return null;
            }

        } catch (Exception e) {
            log.error("交换授权码异常", e);
            return null;
        }
    }

    /**
     * 解析token响应
     * Facebook返回格式: {"access_token":"...", "token_type":"bearer", "expires_in":5183944}
     */
    private TokenResponse parseTokenResponse(String responseBody) {
        try {
            JsonNode jsonNode = objectMapper.readTree(responseBody);
            
            String accessToken = jsonNode.get("access_token").asText();
            String tokenType = jsonNode.has("token_type") ? jsonNode.get("token_type").asText() : "bearer";
            long expiresIn = jsonNode.has("expires_in") ? jsonNode.get("expires_in").asLong() : 3600;
            
            // 计算过期时间
            LocalDateTime expiresAt = LocalDateTime.now().plusSeconds(expiresIn);
            
            log.info("解析token响应成功: tokenType={}, expiresIn={}秒", tokenType, expiresIn);
            
            return new TokenResponse(accessToken, tokenType, expiresAt);
            
        } catch (Exception e) {
            log.error("解析token响应失败: {}", responseBody, e);
            throw new RuntimeException("解析token响应失败", e);
        }
    }

    /**
     * 使用access token获取用户信息
     * Facebook Graph API: https://graph.facebook.com/me?access_token=...
     */
    private String getUserInfo(String accessToken) {
        log.info("开始获取用户信息");
        
        try {
            String userInfoUrl = "https://graph.facebook.com/me?fields=id,name,email&access_token=" + accessToken;
            
            ResponseEntity<String> response = restTemplate.exchange(
                userInfoUrl,
                HttpMethod.GET,
                null,
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("成功获取用户信息");
                return response.getBody();
            } else {
                log.warn("获取用户信息失败: status={}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return null;
        }
    }

    /**
     * 保存用户授权记录
     */
    private void saveUserAuthRecord(String userId, String mediaType, TokenResponse tokenResponse, String userInfo) {
        log.info("保存用户授权记录: userId={}, mediaType={}", userId, mediaType);
        
        try {
            // 查找现有记录或创建新记录
            UserAuthRecord record = userAuthRecordRepository
                .findByUserIdAndMediaType(userId, mediaType)
                .stream()
                .findFirst()
                .orElse(new UserAuthRecord());

            record.setUserId(userId);
            record.setMediaType(mediaType);
            record.setAccessToken(tokenResponse.getAccessToken());
            record.setTokenExpiresAt(tokenResponse.getExpiresAt());
            record.setStatus(UserAuthRecord.AuthStatus.AUTHORIZED);
            record.setUpdatedAt(LocalDateTime.now());

            // 如果有用户信息，可以存储在某个字段中（这里简化处理）
            if (userInfo != null) {
                log.info("用户信息: {}", userInfo);
            }

            userAuthRecordRepository.save(record);
            log.info("用户授权记录保存成功");
            
        } catch (Exception e) {
            log.error("保存用户授权记录失败", e);
            throw new RuntimeException("保存用户授权记录失败", e);
        }
    }

    /**
     * 更新授权记录状态
     */
    private void updateAuthRecordStatus(String userId, String mediaType, UserAuthRecord.AuthStatus status) {
        try {
            Optional<UserAuthRecord> recordOpt = userAuthRecordRepository
                .findByUserIdAndMediaType(userId, mediaType)
                .stream()
                .findFirst();

            if (recordOpt.isPresent()) {
                UserAuthRecord record = recordOpt.get();
                record.setStatus(status);
                userAuthRecordRepository.save(record);
                log.info("更新授权记录状态: userId={}, mediaType={}, status={}", userId, mediaType, status);
            }
        } catch (Exception e) {
            log.error("更新授权记录状态失败", e);
        }
    }

    /**
     * Token响应数据类
     */
    private static class TokenResponse {
        private final String accessToken;
        private final String tokenType;
        private final LocalDateTime expiresAt;

        public TokenResponse(String accessToken, String tokenType, LocalDateTime expiresAt) {
            this.accessToken = accessToken;
            this.tokenType = tokenType;
            this.expiresAt = expiresAt;
        }

        public String getAccessToken() { return accessToken; }
        public String getTokenType() { return tokenType; }
        public LocalDateTime getExpiresAt() { return expiresAt; }
    }
}
