package com.roai.aa.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_auth_record")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserAuthRecord {
    /** 主键ID，使用数据库自增策略生成 */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 用户ID，关联用户系统中的用户标识 */
    @Column(nullable = false)
    private String userId;

    /** 媒体平台类型标识，与MediaAuthInfo中的mediaType对应 */
    @Column(nullable = false)
    private String mediaType;

    /** 访问令牌，用于访问媒体平台API */
    private String accessToken;

    /** 刷新令牌，用于刷新过期的访问令牌 */
    private String refreshToken;

    /** 令牌过期时间 */
    private LocalDateTime tokenExpiresAt;

    /** 授权状态枚举 */
    @Enumerated(EnumType.STRING)
    private AuthStatus status;

    /** 记录创建时间 */
    private LocalDateTime createdAt;
    
    /** 记录最后更新时间 */
    private LocalDateTime updatedAt;

    /** 授权状态枚举定义 */
    public enum AuthStatus {
        PENDING, AUTHORIZED, EXPIRED, REVOKED
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}