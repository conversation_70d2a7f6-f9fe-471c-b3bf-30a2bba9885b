package com.roai.aa.controller;

import com.roai.aa.dto.ApiResponse;
import com.roai.aa.dto.AuthInfoResponse;
import com.roai.aa.entity.UserAuthRecord;
import com.roai.aa.service.MediaAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/media-auth")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
public class MediaAuthController {

    private final MediaAuthService mediaAuthService;
    
    @GetMapping("/auth-info/{mediaType}")
    public ApiResponse<AuthInfoResponse> getAuthInfo(
            @PathVariable String mediaType,
            @RequestParam String userId) {
        try {
            AuthInfoResponse authInfo = mediaAuthService.getAuthInfo(mediaType, userId);
            return ApiResponse.success(authInfo);
        } catch (Exception e) {
            return ApiResponse.error("获取授权信息失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/user-auth/{userId}")
    public ApiResponse<List<UserAuthRecord>> getUserAuthRecords(@PathVariable String userId) {
        try {
            List<UserAuthRecord> records = mediaAuthService.getUserAuthRecords(userId);
            return ApiResponse.success(records);
        } catch (Exception e) {
            return ApiResponse.error("查询用户授权信息失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/callback")
    public ApiResponse<String> handleCallback(@RequestParam Map<String, String> params) {
        try {
            mediaAuthService.handleAuthCallback(params);
            return ApiResponse.success("授权回调处理成功");
        } catch (Exception e) {
            return ApiResponse.error("处理授权回调失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/callback")
    public ApiResponse<String> handleGetCallback(@RequestParam Map<String, String> params) {
        // 有些平台使用GET方式回调
        return handleCallback(params);
    }

    /**
     * Meta (Facebook) 授权回调处理
     * 处理Facebook Login的OAuth2.0回调
     *
     * @param code 授权码
     * @param state 状态参数（包含userId和mediaType）
     * @param error 错误码（如果授权失败）
     * @param errorDescription 错误描述
     */
    @GetMapping("/callback/meta")
    public ApiResponse<String> handleMetaCallback(
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String error,
            @RequestParam(name = "error_description", required = false) String errorDescription) {

        try {
            Map<String, String> params = new HashMap<>();
            if (code != null) params.put("code", code);
            if (state != null) params.put("state", state);
            if (error != null) params.put("error", error);
            if (errorDescription != null) params.put("error_description", errorDescription);

            mediaAuthService.handleAuthCallback(params);
            return ApiResponse.success("Meta授权成功");

        } catch (Exception e) {
            return ApiResponse.error("Meta授权失败: " + e.getMessage());
        }
    }

}