package com.roai.aa.controller;

import com.roai.aa.dto.ApiResponse;
import com.roai.aa.dto.AuthInfoResponse;
import com.roai.aa.entity.UserAuthRecord;
import com.roai.aa.service.MediaAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/media-auth")
@CrossOrigin(origins = "*")
@RequiredArgsConstructor
public class MediaAuthController {

    private final MediaAuthService mediaAuthService;
    
    @GetMapping("/auth-info/{mediaType}")
    public ApiResponse<AuthInfoResponse> getAuthInfo(
            @PathVariable String mediaType,
            @RequestParam String userId) {
        try {
            AuthInfoResponse authInfo = mediaAuthService.getAuthInfo(mediaType, userId);
            return ApiResponse.success(authInfo);
        } catch (Exception e) {
            return ApiResponse.error("获取授权信息失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/user-auth/{userId}")
    public ApiResponse<List<UserAuthRecord>> getUserAuthRecords(@PathVariable String userId) {
        try {
            List<UserAuthRecord> records = mediaAuthService.getUserAuthRecords(userId);
            return ApiResponse.success(records);
        } catch (Exception e) {
            return ApiResponse.error("查询用户授权信息失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/callback")
    public ApiResponse<String> handleCallback(@RequestParam Map<String, String> params) {
        try {
            mediaAuthService.handleAuthCallback(params);
            return ApiResponse.success("授权回调处理成功");
        } catch (Exception e) {
            return ApiResponse.error("处理授权回调失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/callback")
    public ApiResponse<String> handleGetCallback(@RequestParam Map<String, String> params) {
        // 有些平台使用GET方式回调
        return handleCallback(params);
    }
}