package com.roai.aa.dto;

public class AuthInfoResponse {
    private String clientId;
    private String redirectUri;
    private String authUrl;
    private String scope;
    private String state;
    
    public AuthInfoResponse() {}
    
    public AuthInfoResponse(String clientId, String redirectUri, String authUrl, String scope, String state) {
        this.clientId = clientId;
        this.redirectUri = redirectUri;
        this.authUrl = authUrl;
        this.scope = scope;
        this.state = state;
    }
    
    // Getters and Setters
    public String getClientId() { return clientId; }
    public void setClientId(String clientId) { this.clientId = clientId; }
    
    public String getRedirectUri() { return redirectUri; }
    public void setRedirectUri(String redirectUri) { this.redirectUri = redirectUri; }
    
    public String getAuthUrl() { return authUrl; }
    public void setAuthUrl(String authUrl) { this.authUrl = authUrl; }
    
    public String getScope() { return scope; }
    public void setScope(String scope) { this.scope = scope; }
    
    public String getState() { return state; }
    public void setState(String state) { this.state = state; }
}