package com.roai.aa.dto;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AuthInfoResponse {
    // Getters and Setters
    private String clientId;
    private String redirectUri;
    private String authUrl;
    private String scope;
    private String state;
    
    public AuthInfoResponse() {}
    
    public AuthInfoResponse(String clientId, String redirectUri, String authUrl, String scope, String state) {
        this.clientId = clientId;
        this.redirectUri = redirectUri;
        this.authUrl = authUrl;
        this.scope = scope;
        this.state = state;
    }

}